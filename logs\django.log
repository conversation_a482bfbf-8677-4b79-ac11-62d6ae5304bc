INFO 2025-06-11 13:03:24,786 autoreload 24508 17160 Watching for file changes with StatReloader
WARNING 2025-06-11 13:03:32,123 log 24508 19068 Not Found: /
WARNING 2025-06-11 13:03:32,124 basehttp 24508 19068 "GET / HTTP/1.1" 404 2682
INFO 2025-06-11 13:03:36,251 basehttp 24508 19068 "GET /admin HTTP/1.1" 301 0
INFO 2025-06-11 13:03:36,288 basehttp 24508 1384 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:03:36,363 basehttp 24508 1384 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4197
INFO 2025-06-11 13:03:36,476 basehttp 24508 23548 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-06-11 13:03:36,477 basehttp 24508 18708 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-06-11 13:03:36,485 basehttp 24508 23548 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-06-11 13:03:36,516 basehttp 24508 1384 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-06-11 13:03:36,525 basehttp 24508 10876 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-06-11 13:03:36,549 basehttp 24508 17804 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-06-11 13:03:36,557 basehttp 24508 23488 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-11 13:03:44,731 backends 24508 1384 Successful email authentication for: <EMAIL>
ERROR 2025-06-11 13:03:48,943 log 24508 1384 Internal Server Error: /admin/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 29, in _decorator
    return method(self, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 138, in has_key
    return self.client.has_key(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\client\default.py", line 670, in has_key
    raise ConnectionInterrupted(connection=client) from e
django_redis.exceptions.ConnectionInterrupted: Redis ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\admin\sites.py", line 441, in login
    return LoginView.as_view(**defaults)(request)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\debug.py", line 92, in sensitive_post_parameters_wrapper
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 90, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\views\generic\edit.py", line 153, in post
    return self.form_valid(form)
           ~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\views.py", line 109, in form_valid
    auth_login(self.request, form.get_user())
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\auth\__init__.py", line 118, in login
    request.session.cycle_key()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 304, in cycle_key
    self.create()
    ~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 42, in create
    self._session_key = self._get_new_session_key()
                        ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\base.py", line 150, in _get_new_session_key
    if not self.exists(session_key):
           ~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\contrib\sessions\backends\cache.py", line 73, in exists
    bool(session_key) and (self.cache_key_prefix + session_key) in self._cache
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django\core\cache\backends\base.py", line 299, in __contains__
    return self.has_key(key)
           ~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\cache.py", line 36, in _decorator
    raise e.__cause__
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\django_redis\client\default.py", line 668, in has_key
    return client.exists(key) == 1
           ~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\commands\core.py", line 1736, in exists
    return self.execute_command("EXISTS", *names)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\client.py", line 533, in execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
                              ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\connection.py", line 1086, in get_connection
    connection.connect()
    ~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\venv\Lib\site-packages\redis\connection.py", line 270, in connect
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6379. No connection could be made because the target machine actively refused it.
ERROR 2025-06-11 13:03:48,968 basehttp 24508 1384 "POST /admin/login/?next=/admin/ HTTP/1.1" 500 210899
INFO 2025-06-11 13:05:02,199 autoreload 24508 17160 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:05:03,957 autoreload 4808 6884 Watching for file changes with StatReloader
INFO 2025-06-11 13:05:55,079 autoreload 4808 6884 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:05:56,618 autoreload 14368 2856 Watching for file changes with StatReloader
INFO 2025-06-11 13:06:32,006 autoreload 9848 17552 Watching for file changes with StatReloader
INFO 2025-06-11 13:08:30,839 autoreload 5792 17732 Watching for file changes with StatReloader
INFO 2025-06-11 13:09:23,784 autoreload 5792 17732 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:09:25,086 autoreload 22464 17160 Watching for file changes with StatReloader
INFO 2025-06-11 13:10:42,080 autoreload 14884 24628 Watching for file changes with StatReloader
INFO 2025-06-11 13:11:35,284 autoreload 14884 24628 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:11:36,683 autoreload 23888 24596 Watching for file changes with StatReloader
INFO 2025-06-11 13:11:45,333 autoreload 23888 24596 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:11:46,797 autoreload 25592 8904 Watching for file changes with StatReloader
INFO 2025-06-11 13:11:55,609 autoreload 25592 8904 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:11:56,975 autoreload 25264 15680 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:04,564 autoreload 25264 15680 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:06,521 autoreload 4544 25308 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:15,244 autoreload 4544 25308 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:16,870 autoreload 23096 7820 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:26,474 autoreload 23096 7820 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:27,931 autoreload 25464 21552 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:38,614 autoreload 25464 21552 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 13:12:40,085 autoreload 14820 5684 Watching for file changes with StatReloader
INFO 2025-06-11 13:12:48,879 autoreload 14820 5684 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 13:12:50,115 autoreload 10444 5600 Watching for file changes with StatReloader
INFO 2025-06-11 13:13:33,874 autoreload 19840 24192 Watching for file changes with StatReloader
INFO 2025-06-11 13:13:37,937 basehttp 19840 25516 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:13:38,015 basehttp 19840 25516 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4197
INFO 2025-06-11 13:13:38,063 basehttp 19840 25516 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,071 basehttp 19840 25516 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,075 basehttp 19840 19732 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,081 basehttp 19840 24308 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,092 basehttp 19840 9052 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,096 basehttp 19840 18548 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-11 13:13:38,096 basehttp 19840 24624 "GET /static/admin/css/login.css HTTP/1.1" 304 0
WARNING 2025-06-11 13:13:38,162 log 19840 19732 Not Found: /favicon.ico
INFO 2025-06-11 13:13:38,163 basehttp 19840 19732 - Broken pipe from ('127.0.0.1', 58888)
INFO 2025-06-11 13:13:45,111 backends 19840 25516 Successful email authentication for: <EMAIL>
INFO 2025-06-11 13:13:45,270 basehttp 19840 25516 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:13:45,504 basehttp 19840 25516 "GET /admin/ HTTP/1.1" 200 6789
INFO 2025-06-11 13:13:45,618 basehttp 19840 24308 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-11 13:13:45,628 basehttp 19840 24308 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:13:45,628 basehttp 19840 18548 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-06-11 13:13:45,629 basehttp 19840 9052 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-11 13:13:51,968 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 14407
INFO 2025-06-11 13:13:51,998 basehttp 19840 18548 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
INFO 2025-06-11 13:13:52,005 basehttp 19840 24624 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-06-11 13:13:52,006 basehttp 19840 25516 "GET /static/admin/js/core.js HTTP/1.1" 200 5682
INFO 2025-06-11 13:13:52,009 basehttp 19840 18548 "GET /static/admin/js/actions.js HTTP/1.1" 200 7872
INFO 2025-06-11 13:13:52,010 basehttp 19840 9052 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
INFO 2025-06-11 13:13:52,012 basehttp 19840 25076 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
INFO 2025-06-11 13:13:52,019 basehttp 19840 24624 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-06-11 13:13:52,025 basehttp 19840 25516 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-06-11 13:13:52,030 basehttp 19840 9052 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-11 13:13:52,034 basehttp 19840 18548 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-06-11 13:13:52,227 basehttp 19840 24308 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:13:52,231 basehttp 19840 9052 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-11 13:13:52,232 basehttp 19840 18548 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-11 13:13:52,263 basehttp 19840 9052 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:13:52,263 basehttp 19840 18548 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-06-11 13:13:56,832 basehttp 19840 9052 "GET /admin/authentication/user/add/ HTTP/1.1" 200 13013
INFO 2025-06-11 13:13:56,862 basehttp 19840 9052 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-06-11 13:13:56,865 basehttp 19840 9052 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-06-11 13:13:56,867 basehttp 19840 25076 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-06-11 13:13:56,881 basehttp 19840 9052 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-06-11 13:13:57,063 basehttp 19840 18548 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:14:39,443 basehttp 19840 9052 "POST /admin/authentication/user/add/ HTTP/1.1" 302 0
INFO 2025-06-11 13:14:40,025 basehttp 19840 9052 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23588
INFO 2025-06-11 13:14:40,059 basehttp 19840 25076 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-06-11 13:14:40,060 basehttp 19840 24624 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-06-11 13:14:40,060 basehttp 19840 24308 "GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
INFO 2025-06-11 13:14:40,061 basehttp 19840 25516 "GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15292
INFO 2025-06-11 13:14:40,071 basehttp 19840 18548 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-06-11 13:14:40,308 basehttp 19840 9052 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:14:40,386 basehttp 19840 18548 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
INFO 2025-06-11 13:14:40,389 basehttp 19840 24624 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
WARNING 2025-06-11 13:14:40,391 log 19840 9052 Not Found: /favicon.ico
INFO 2025-06-11 13:14:40,393 basehttp 19840 9052 - Broken pipe from ('127.0.0.1', 58890)
INFO 2025-06-11 13:14:49,491 basehttp 19840 18548 "GET /admin/authentication/user/ HTTP/1.1" 200 15073
INFO 2025-06-11 13:14:49,767 basehttp 19840 18548 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:14:49,779 basehttp 19840 24624 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
WARNING 2025-06-11 13:14:49,865 log 19840 18548 Not Found: /favicon.ico
INFO 2025-06-11 13:14:49,871 basehttp 19840 18548 - Broken pipe from ('127.0.0.1', 58891)
INFO 2025-06-11 13:14:56,756 basehttp 19840 24624 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23334
INFO 2025-06-11 13:14:57,043 basehttp 19840 24624 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:14:57,111 log 19840 24624 Not Found: /favicon.ico
INFO 2025-06-11 13:14:57,112 basehttp 19840 24624 - Broken pipe from ('127.0.0.1', 58892)
INFO 2025-06-11 13:15:10,555 basehttp 19840 25516 "POST /admin/authentication/user/2/change/ HTTP/1.1" 302 0
INFO 2025-06-11 13:15:10,916 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 15292
INFO 2025-06-11 13:15:11,188 basehttp 19840 25076 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:15:11,266 log 19840 25076 Not Found: /favicon.ico
INFO 2025-06-11 13:15:11,268 basehttp 19840 25076 - Broken pipe from ('127.0.0.1', 58901)
INFO 2025-06-11 13:15:21,055 basehttp 19840 25516 "GET /admin/auth/group/ HTTP/1.1" 200 8153
INFO 2025-06-11 13:15:21,297 basehttp 19840 24308 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:15:21,348 log 19840 24308 Not Found: /favicon.ico
INFO 2025-06-11 13:15:21,354 basehttp 19840 24308 - Broken pipe from ('127.0.0.1', 58889)
INFO 2025-06-11 13:15:24,268 basehttp 19840 25516 "GET /admin/auth/group/add/ HTTP/1.1" 200 12132
INFO 2025-06-11 13:15:24,519 basehttp 19840 25516 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:15:24,595 basehttp 19840 23288 "GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
INFO 2025-06-11 13:15:24,595 basehttp 19840 22820 "GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
INFO 2025-06-11 13:15:24,597 basehttp 19840 25516 "GET /static/admin/img/icon-unknown-alt.svg HTTP/1.1" 200 655
INFO 2025-06-11 13:15:28,492 basehttp 19840 25060 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-11 13:15:39,280 basehttp 19840 25516 "POST /admin/auth/group/add/ HTTP/1.1" 302 0
INFO 2025-06-11 13:15:39,607 basehttp 19840 25516 "GET /admin/auth/group/ HTTP/1.1" 200 9498
INFO 2025-06-11 13:15:39,875 basehttp 19840 22820 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:15:43,357 basehttp 19840 25516 "GET /admin/auth/group/add/ HTTP/1.1" 200 12132
INFO 2025-06-11 13:15:43,663 basehttp 19840 22820 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:16:25,086 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 15065
INFO 2025-06-11 13:16:25,113 basehttp 19840 22820 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,115 basehttp 19840 23288 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,127 basehttp 19840 22820 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,127 basehttp 19840 23288 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,129 basehttp 19840 6312 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,132 basehttp 19840 22820 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,142 basehttp 19840 22392 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,142 basehttp 19840 23288 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,143 basehttp 19840 1672 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,144 basehttp 19840 6312 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,146 basehttp 19840 22820 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,148 basehttp 19840 22392 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,151 basehttp 19840 23288 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,155 basehttp 19840 1672 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,159 basehttp 19840 6312 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,162 basehttp 19840 22820 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:25,393 basehttp 19840 25516 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 13:16:25,401 basehttp 19840 22820 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-11 13:16:25,431 basehttp 19840 22820 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-11 13:16:25,441 basehttp 19840 23288 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:16:25,441 basehttp 19840 22820 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-11 13:16:25,442 basehttp 19840 22392 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
WARNING 2025-06-11 13:16:25,474 log 19840 22820 Not Found: /favicon.ico
INFO 2025-06-11 13:16:25,475 basehttp 19840 22820 - Broken pipe from ('127.0.0.1', 58954)
INFO 2025-06-11 13:16:30,278 basehttp 19840 25516 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23384
INFO 2025-06-11 13:16:30,328 basehttp 19840 1672 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,328 basehttp 19840 23288 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,329 basehttp 19840 6312 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,345 basehttp 19840 23288 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
INFO 2025-06-11 13:16:30,540 basehttp 19840 22392 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:16:30,599 log 19840 22392 Not Found: /favicon.ico
INFO 2025-06-11 13:16:30,605 basehttp 19840 22392 - Broken pipe from ('127.0.0.1', 58981)
INFO 2025-06-11 13:16:41,535 basehttp 19840 25516 "POST /admin/authentication/user/2/change/ HTTP/1.1" 302 0
INFO 2025-06-11 13:16:41,829 basehttp 19840 25516 "GET /admin/authentication/user/ HTTP/1.1" 200 15292
INFO 2025-06-11 13:16:42,085 basehttp 19840 23288 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-11 13:16:42,132 log 19840 23288 Not Found: /favicon.ico
INFO 2025-06-11 13:16:42,136 basehttp 19840 23288 - Broken pipe from ('127.0.0.1', 58953)
WARNING 2025-06-11 13:17:01,393 log 19840 25516 Not Found: /api
WARNING 2025-06-11 13:17:01,394 basehttp 19840 25516 "GET /api HTTP/1.1" 404 2709
WARNING 2025-06-11 13:17:08,019 log 19840 25516 Not Found: /api/auth
WARNING 2025-06-11 13:17:08,019 basehttp 19840 25516 "GET /api/auth HTTP/1.1" 404 2724
INFO 2025-06-11 13:17:28,059 basehttp 19840 25516 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-06-11 13:17:28,341 basehttp 19840 1672 "GET /api/docs/ HTTP/1.1" 200 4474
INFO 2025-06-11 13:17:29,126 basehttp 19840 1672 "GET /api/schema/ HTTP/1.1" 200 16225
INFO 2025-06-11 14:50:57,760 autoreload 26588 23052 Watching for file changes with StatReloader
WARNING 2025-06-11 14:51:40,153 log 26588 6916 Bad Request: /api/auth/register/mobile/
WARNING 2025-06-11 14:51:40,154 basehttp 26588 6916 "POST /api/auth/register/mobile/ HTTP/1.1" 400 80
INFO 2025-06-11 14:55:34,163 autoreload 26588 23052 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 14:55:36,462 autoreload 22664 26936 Watching for file changes with StatReloader
INFO 2025-06-11 14:55:50,117 autoreload 22664 26936 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 14:55:51,793 autoreload 23020 11188 Watching for file changes with StatReloader
INFO 2025-06-11 14:56:04,146 autoreload 23020 11188 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 14:56:05,604 autoreload 19488 7076 Watching for file changes with StatReloader
INFO 2025-06-11 15:00:12,857 autoreload 22544 4232 Watching for file changes with StatReloader
INFO 2025-06-11 15:00:53,564 basehttp 19488 20548 "GET /api/docs/ HTTP/1.1" 200 4474
INFO 2025-06-11 15:00:54,206 basehttp 19488 20548 "GET /api/schema/ HTTP/1.1" 200 24726
ERROR 2025-06-11 15:19:15,141 utils 19488 11448 MSG91 API error: 401 - {"status":"fail","hasError":true,"errors":"Unauthorized","code":"401"}
ERROR 2025-06-11 15:19:15,147 utils 19488 11448 Failed to send OTP to +919052555004
ERROR 2025-06-11 15:19:15,343 log 19488 11448 Internal Server Error: /api/auth/register/mobile/
ERROR 2025-06-11 15:19:15,344 basehttp 19488 11448 "POST /api/auth/register/mobile/ HTTP/1.1" 500 49
INFO 2025-06-11 15:19:34,152 basehttp 19488 8852 "GET /admin/authentication/user/ HTTP/1.1" 200 15065
INFO 2025-06-11 15:19:34,210 basehttp 19488 8852 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,217 basehttp 19488 4344 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,229 basehttp 19488 7752 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,232 basehttp 19488 26836 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,237 basehttp 19488 25392 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,237 basehttp 19488 7752 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,243 basehttp 19488 26836 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,251 basehttp 19488 25392 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,253 basehttp 19488 7752 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,258 basehttp 19488 26836 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,259 basehttp 19488 25392 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,268 basehttp 19488 7752 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,269 basehttp 19488 26836 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,274 basehttp 19488 25392 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:34,360 basehttp 19488 8852 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-11 15:19:34,365 basehttp 19488 4344 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-06-11 15:19:34,477 basehttp 19488 14476 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 15:19:34,519 basehttp 19488 4344 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-11 15:19:34,520 basehttp 19488 14476 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-11 15:19:34,545 basehttp 19488 4344 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-11 15:19:34,550 basehttp 19488 14476 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-11 15:19:34,555 basehttp 19488 4344 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
WARNING 2025-06-11 15:19:34,655 log 19488 4344 Not Found: /favicon.ico
INFO 2025-06-11 15:19:34,657 basehttp 19488 4344 - Broken pipe from ('127.0.0.1', 61920)
INFO 2025-06-11 15:19:37,691 basehttp 19488 8852 "GET /admin/authentication/user/2/change/ HTTP/1.1" 200 23392
INFO 2025-06-11 15:19:37,735 basehttp 19488 8852 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-06-11 15:19:37,737 basehttp 19488 7752 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-06-11 15:19:37,741 basehttp 19488 8852 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-06-11 15:19:37,742 basehttp 19488 26836 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-06-11 15:19:37,747 basehttp 19488 7752 "GET /static/admin/js/SelectBox.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:37,747 basehttp 19488 25392 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-06-11 15:19:37,756 basehttp 19488 8852 "GET /static/admin/js/SelectFilter2.js HTTP/1.1" 304 0
INFO 2025-06-11 15:19:37,757 basehttp 19488 22284 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-06-11 15:19:37,761 basehttp 19488 26836 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-06-11 15:19:37,965 basehttp 19488 14476 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 15:19:38,035 basehttp 19488 26836 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
INFO 2025-06-11 15:19:38,040 basehttp 19488 26836 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
INFO 2025-06-11 15:19:44,433 basehttp 19488 8852 "GET /admin/auth/group/ HTTP/1.1" 200 9299
INFO 2025-06-11 15:19:44,692 basehttp 19488 26836 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
ERROR 2025-06-11 15:19:57,354 utils 19488 11448 MSG91 API error: 401 - {"status":"fail","hasError":true,"errors":"Unauthorized","code":"401"}
ERROR 2025-06-11 15:19:57,358 utils 19488 11448 Failed to send OTP to +919052555004
ERROR 2025-06-11 15:19:57,540 log 19488 11448 Internal Server Error: /api/auth/register/mobile/
ERROR 2025-06-11 15:19:57,542 basehttp 19488 11448 "POST /api/auth/register/mobile/ HTTP/1.1" 500 49
INFO 2025-06-11 15:20:40,004 autoreload 19488 7076 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:20:40,037 autoreload 22544 4232 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:20:42,172 autoreload 20860 26984 Watching for file changes with StatReloader
INFO 2025-06-11 15:20:42,180 autoreload 27184 17736 Watching for file changes with StatReloader
INFO 2025-06-11 15:20:53,555 autoreload 20860 26984 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:20:54,202 autoreload 27184 17736 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:20:55,339 autoreload 25728 18656 Watching for file changes with StatReloader
INFO 2025-06-11 15:20:55,924 autoreload 3532 15436 Watching for file changes with StatReloader
INFO 2025-06-11 15:21:26,390 autoreload 25728 18656 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:21:26,709 autoreload 3532 15436 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:21:28,870 autoreload 11784 22656 Watching for file changes with StatReloader
INFO 2025-06-11 15:21:28,924 autoreload 26384 23728 Watching for file changes with StatReloader
INFO 2025-06-11 15:21:41,516 autoreload 11784 22656 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:21:41,600 autoreload 26384 23728 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:21:43,930 autoreload 25744 17468 Watching for file changes with StatReloader
INFO 2025-06-11 15:21:43,999 autoreload 22116 27128 Watching for file changes with StatReloader
INFO 2025-06-11 15:22:02,581 autoreload 22116 27128 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:22:02,824 autoreload 25744 17468 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:22:04,679 autoreload 12588 24620 Watching for file changes with StatReloader
INFO 2025-06-11 15:22:04,901 autoreload 20948 19360 Watching for file changes with StatReloader
INFO 2025-06-11 15:22:17,006 autoreload 20948 19360 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 15:22:17,068 autoreload 12588 24620 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-11 15:22:18,859 autoreload 25700 4232 Watching for file changes with StatReloader
INFO 2025-06-11 15:22:18,904 autoreload 26276 27500 Watching for file changes with StatReloader
INFO 2025-06-11 15:23:24,419 autoreload 26276 27500 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:23:25,263 autoreload 25700 4232 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:23:26,151 autoreload 26484 26668 Watching for file changes with StatReloader
INFO 2025-06-11 15:23:26,889 autoreload 12264 20892 Watching for file changes with StatReloader
INFO 2025-06-11 15:23:51,313 autoreload 26484 26668 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:23:52,766 autoreload 15588 13028 Watching for file changes with StatReloader
INFO 2025-06-11 15:24:13,163 autoreload 15588 13028 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\utils.py changed, reloading.
INFO 2025-06-11 15:24:14,707 autoreload 18336 26768 Watching for file changes with StatReloader
INFO 2025-06-11 15:24:53,818 autoreload 27272 10968 Watching for file changes with StatReloader
INFO 2025-06-11 15:27:29,737 basehttp 27272 25560 "GET /api/docs HTTP/1.1" 301 0
INFO 2025-06-11 15:27:30,119 basehttp 27272 18320 "GET /api/docs/ HTTP/1.1" 200 4665
INFO 2025-06-11 15:27:30,848 basehttp 27272 18320 "GET /api/schema/ HTTP/1.1" 200 24726
INFO 2025-06-11 15:27:45,902 utils 27272 7752 Sending OTP to 9052555004 via MSG91
INFO 2025-06-11 15:27:46,323 utils 27272 7752 MSG91 Response: 200 - {"request_id":"35666b6f415851487a707730","type":"success"}
INFO 2025-06-11 15:27:46,325 utils 27272 7752 OTP sent successfully to +919052555004
INFO 2025-06-11 15:27:46,326 basehttp 27272 7752 "POST /api/auth/register/mobile/ HTTP/1.1" 201 143
WARNING 2025-06-11 15:28:52,950 log 27272 18320 Bad Request: /api/auth/otp/verify/
WARNING 2025-06-11 15:28:52,951 basehttp 27272 18320 "POST /api/auth/otp/verify/ HTTP/1.1" 400 79
WARNING 2025-06-11 15:29:50,378 log 27272 18448 Bad Request: /api/auth/otp/verify/
WARNING 2025-06-11 15:29:50,378 basehttp 27272 18448 "POST /api/auth/otp/verify/ HTTP/1.1" 400 71
WARNING 2025-06-11 15:30:23,299 log 27272 18448 Bad Request: /api/auth/otp/verify/
WARNING 2025-06-11 15:30:23,299 basehttp 27272 18448 "POST /api/auth/otp/verify/ HTTP/1.1" 400 71
INFO 2025-06-11 15:31:18,400 utils 27272 18448 Verifying OTP for 9052555004 via MSG91
INFO 2025-06-11 15:31:18,774 utils 27272 18448 MSG91 Verify Response: 200 - {"message":"OTP verified success","type":"success"}
INFO 2025-06-11 15:31:18,777 utils 27272 18448 OTP verified successfully for +919052555004
INFO 2025-06-11 15:31:19,048 basehttp 27272 18448 "POST /api/auth/otp/verify/ HTTP/1.1" 200 60
INFO 2025-06-11 15:35:08,849 autoreload 27272 10968 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:35:10,902 autoreload 23916 18348 Watching for file changes with StatReloader
INFO 2025-06-11 15:35:28,691 autoreload 23916 18348 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\urls.py changed, reloading.
INFO 2025-06-11 15:35:30,060 autoreload 18080 17432 Watching for file changes with StatReloader
INFO 2025-06-11 15:36:07,793 autoreload 18080 17432 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:36:09,305 autoreload 12568 25444 Watching for file changes with StatReloader
INFO 2025-06-11 15:36:25,666 autoreload 12568 25444 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:36:27,077 autoreload 8300 21864 Watching for file changes with StatReloader
INFO 2025-06-11 15:36:47,946 autoreload 8300 21864 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-11 15:36:49,631 autoreload 25940 23236 Watching for file changes with StatReloader
INFO 2025-06-11 15:39:50,563 basehttp 25940 26196 "GET /api/docs/ HTTP/1.1" 200 4665
INFO 2025-06-11 15:39:51,180 basehttp 25940 26196 "GET /api/schema/ HTTP/1.1" 200 33708
INFO 2025-06-11 15:42:01,611 basehttp 25940 26720 "GET /admin/authentication/user/ HTTP/1.1" 200 15717
INFO 2025-06-11 15:42:01,666 basehttp 25940 26616 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,668 basehttp 25940 19420 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,669 basehttp 25940 15256 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,677 basehttp 25940 2244 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,679 basehttp 25940 26616 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,680 basehttp 25940 19420 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,684 basehttp 25940 15256 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,684 basehttp 25940 2244 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,692 basehttp 25940 26616 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,701 basehttp 25940 15256 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,702 basehttp 25940 26616 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,702 basehttp 25940 19420 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,702 basehttp 25940 2244 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,711 basehttp 25940 26616 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,714 basehttp 25940 2244 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:01,752 basehttp 25940 26720 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-11 15:42:01,785 basehttp 25940 19420 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-11 15:42:01,884 basehttp 25940 21620 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 15:42:01,917 basehttp 25940 21620 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-11 15:42:01,928 basehttp 25940 15256 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-11 15:42:01,930 basehttp 25940 26616 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-06-11 15:42:01,934 basehttp 25940 19420 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
WARNING 2025-06-11 15:42:01,999 log 25940 21620 Not Found: /favicon.ico
INFO 2025-06-11 15:42:02,000 basehttp 25940 21620 - Broken pipe from ('127.0.0.1', 62898)
INFO 2025-06-11 15:42:06,445 basehttp 25940 26720 "GET /admin/authentication/user/5/change/ HTTP/1.1" 200 23147
INFO 2025-06-11 15:42:06,499 basehttp 25940 26720 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:06,518 basehttp 25940 19420 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-06-11 15:42:06,530 basehttp 25940 2244 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-06-11 15:42:06,531 basehttp 25940 26720 "GET /static/admin/js/SelectFilter2.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:06,537 basehttp 25940 19420 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:06,542 basehttp 25940 15256 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-06-11 15:42:06,543 basehttp 25940 27208 "GET /static/admin/js/SelectBox.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:06,543 basehttp 25940 2244 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
INFO 2025-06-11 15:42:06,562 basehttp 25940 26720 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-06-11 15:42:06,764 basehttp 25940 26616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 15:42:06,856 basehttp 25940 15256 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
INFO 2025-06-11 15:42:06,868 basehttp 25940 19420 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
INFO 2025-06-11 15:42:16,529 basehttp 25940 26720 "GET /admin/authentication/user/5/password/ HTTP/1.1" 200 8077
INFO 2025-06-11 15:42:24,392 basehttp 25940 26720 "GET /admin/authentication/user/ HTTP/1.1" 200 15717
INFO 2025-06-11 15:42:24,654 basehttp 25940 19420 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-11 15:42:28,806 basehttp 25940 26720 "GET /admin/authentication/user/1/change/ HTTP/1.1" 200 23419
INFO 2025-06-11 15:42:29,103 basehttp 25940 19420 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-13 17:31:01,811 autoreload 36992 23512 Watching for file changes with StatReloader
WARNING 2025-06-13 17:31:09,127 log 36992 36284 Not Found: /
WARNING 2025-06-13 17:31:09,128 basehttp 36992 36284 "GET / HTTP/1.1" 404 3355
INFO 2025-06-13 17:31:14,063 basehttp 36992 36284 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-13 17:31:14,310 basehttp 36992 36284 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4187
INFO 2025-06-13 17:31:14,424 basehttp 36992 35540 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-13 17:31:14,424 basehttp 36992 33172 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-13 17:31:14,438 basehttp 36992 18768 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-13 17:31:14,445 basehttp 36992 33172 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-13 17:31:14,466 basehttp 36992 28992 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-13 17:31:14,570 basehttp 36992 36284 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-06-13 17:31:14,574 basehttp 36992 32976 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-06-13 17:31:21,802 backends 36992 32976 Successful email authentication for: <EMAIL>
INFO 2025-06-13 17:31:21,957 basehttp 36992 32976 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-06-13 17:31:22,293 basehttp 36992 32976 "GET /admin/ HTTP/1.1" 200 13648
INFO 2025-06-13 17:31:22,450 basehttp 36992 32976 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-13 17:31:22,507 basehttp 36992 32976 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-13 17:31:22,515 basehttp 36992 35540 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-13 17:31:22,522 basehttp 36992 18768 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
WARNING 2025-06-13 17:31:22,762 log 36992 32976 Not Found: /favicon.ico
WARNING 2025-06-13 17:31:22,788 basehttp 36992 32976 "GET /favicon.ico HTTP/1.1" 404 3406
WARNING 2025-06-13 17:31:22,871 log 36992 32976 Not Found: /favicon.ico
WARNING 2025-06-13 17:31:22,873 basehttp 36992 32976 "GET /favicon.ico HTTP/1.1" 404 3406
INFO 2025-06-14 11:01:30,289 autoreload 33704 15908 Watching for file changes with StatReloader
INFO 2025-06-14 11:01:37,129 basehttp 33704 25256 "OPTIONS /api/auth/otp/send/ HTTP/1.1" 200 0
INFO 2025-06-14 11:01:37,139 utils 33704 25256 Sending OTP to 8328315313 via MSG91
INFO 2025-06-14 11:01:38,547 utils 33704 25256 MSG91 Response: 200 - {"request_id":"35666e6b614c795a44347157","type":"success"}
INFO 2025-06-14 11:01:38,549 utils 33704 25256 OTP sent successfully to +918328315313
INFO 2025-06-14 11:01:38,550 basehttp 33704 25256 "POST /api/auth/otp/send/ HTTP/1.1" 200 67
INFO 2025-06-14 11:05:54,610 utils 33704 32524 Sending OTP to 8328315313 via MSG91
INFO 2025-06-14 11:05:55,474 utils 33704 32524 MSG91 Response: 200 - {"request_id":"35666e6b6533723556424653","type":"success"}
INFO 2025-06-14 11:05:55,476 utils 33704 32524 OTP sent successfully to +918328315313
INFO 2025-06-14 11:05:55,478 basehttp 33704 32524 "POST /api/auth/otp/send/ HTTP/1.1" 200 67
INFO 2025-06-14 11:12:12,175 utils 33704 33420 Sending OTP to 8328315313 via MSG91
INFO 2025-06-14 11:12:13,113 utils 33704 33420 MSG91 Response: 200 - {"request_id":"35666e6b6c6c726c45715972","type":"success"}
INFO 2025-06-14 11:12:13,115 utils 33704 33420 OTP sent successfully to +918328315313
INFO 2025-06-14 11:12:13,117 basehttp 33704 33420 "POST /api/auth/otp/send/ HTTP/1.1" 200 67
INFO 2025-06-14 11:12:30,021 basehttp 33704 25100 "OPTIONS /api/auth/login/mobile/ HTTP/1.1" 200 0
WARNING 2025-06-14 11:12:30,309 backends 33704 25100 Authentication attempt for non-existent mobile: +918328315313
WARNING 2025-06-14 11:12:30,311 log 33704 25100 Unauthorized: /api/auth/login/mobile/
WARNING 2025-06-14 11:12:30,313 basehttp 33704 25100 "POST /api/auth/login/mobile/ HTTP/1.1" 401 41
INFO 2025-06-14 11:45:09,125 autoreload 33704 15908 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-14 11:45:14,933 autoreload 22464 5720 Watching for file changes with StatReloader
INFO 2025-06-14 11:45:31,917 autoreload 22464 5720 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\authentication\views.py changed, reloading.
INFO 2025-06-14 11:45:36,957 autoreload 2900 30376 Watching for file changes with StatReloader
INFO 2025-06-14 11:56:40,944 autoreload 6828 27236 Watching for file changes with StatReloader
WARNING 2025-06-14 11:57:15,719 log 6828 29484 Not Found: /
WARNING 2025-06-14 11:57:15,721 basehttp 6828 29484 "GET / HTTP/1.1" 404 3355
WARNING 2025-06-14 11:57:16,055 log 6828 21128 Not Found: /favicon.ico
WARNING 2025-06-14 11:57:16,056 basehttp 6828 21128 "GET /favicon.ico HTTP/1.1" 404 3406
INFO 2025-06-14 11:57:22,790 basehttp 6828 21128 "GET /admin/ HTTP/1.1" 200 13648
INFO 2025-06-14 11:57:22,888 basehttp 6828 19940 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:22,888 basehttp 6828 39352 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-14 11:57:22,893 basehttp 6828 19940 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-14 11:57:22,893 basehttp 6828 37112 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:22,908 basehttp 6828 3536 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-06-14 11:57:23,066 basehttp 6828 21128 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-06-14 11:57:23,068 basehttp 6828 29484 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-06-14 11:57:23,103 basehttp 6828 21128 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-14 11:57:23,108 basehttp 6828 37112 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-06-14 11:57:23,118 basehttp 6828 39352 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-14 11:57:29,064 basehttp 6828 21128 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19159
INFO 2025-06-14 11:57:29,171 basehttp 6828 19940 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,185 basehttp 6828 3536 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,203 basehttp 6828 39352 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-06-14 11:57:29,209 basehttp 6828 29484 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-06-14 11:57:29,238 basehttp 6828 19940 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,259 basehttp 6828 3536 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,304 basehttp 6828 39352 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-06-14 11:57:29,307 basehttp 6828 29484 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,310 basehttp 6828 19940 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,311 basehttp 6828 21128 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-06-14 11:57:29,320 basehttp 6828 3536 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,326 basehttp 6828 39352 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-06-14 11:57:29,356 basehttp 6828 21128 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-06-14 11:57:29,440 basehttp 6828 37112 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 11:57:45,558 basehttp 6828 21128 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 11:57:46,185 basehttp 6828 21128 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19442
INFO 2025-06-14 11:57:46,496 basehttp 6828 21128 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 11:57:46,591 basehttp 6828 21128 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-14 11:58:01,443 basehttp 6828 21128 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 11:58:02,001 basehttp 6828 21128 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19482
INFO 2025-06-14 11:58:02,319 basehttp 6828 21128 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 11:58:23,993 basehttp 6828 21128 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 11:58:24,564 basehttp 6828 21128 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19524
INFO 2025-06-14 11:58:24,872 basehttp 6828 21128 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 11:58:36,368 basehttp 6828 21128 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 11:58:36,855 basehttp 6828 21128 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19567
INFO 2025-06-14 11:58:37,022 basehttp 6828 35532 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-14 11:58:37,171 basehttp 6828 21128 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 11:58:55,041 basehttp 6828 21128 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 11:58:55,798 basehttp 6828 21128 "GET /admin/catalogue/category/ HTTP/1.1" 200 19068
INFO 2025-06-14 11:58:55,925 basehttp 6828 39352 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
INFO 2025-06-14 11:58:55,934 basehttp 6828 37112 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-14 11:58:56,098 basehttp 6828 21128 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 11:58:56,120 basehttp 6828 39352 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-14 11:58:56,160 basehttp 6828 21128 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-14 11:58:59,743 basehttp 6828 21128 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19374
INFO 2025-06-14 11:59:00,057 basehttp 6828 21128 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:00:04,046 basehttp 6828 32788 "OPTIONS /api/cart/ HTTP/1.1" 200 0
INFO 2025-06-14 12:00:04,058 basehttp 6828 4756 "OPTIONS /api/cart/ HTTP/1.1" 200 0
INFO 2025-06-14 12:00:04,066 basehttp 6828 36756 "OPTIONS /api/cart/summary/ HTTP/1.1" 200 0
INFO 2025-06-14 12:00:04,070 basehttp 6828 6048 "OPTIONS /api/cart/summary/ HTTP/1.1" 200 0
INFO 2025-06-14 12:00:04,647 basehttp 6828 36756 "GET /api/cart/summary/ HTTP/1.1" 200 246
INFO 2025-06-14 12:00:04,670 basehttp 6828 32788 "GET /api/cart/ HTTP/1.1" 200 383
INFO 2025-06-14 12:00:05,338 basehttp 6828 36756 "GET /api/cart/summary/ HTTP/1.1" 200 246
INFO 2025-06-14 12:00:05,380 basehttp 6828 32788 "GET /api/cart/ HTTP/1.1" 200 383
INFO 2025-06-14 12:02:27,244 basehttp 6828 25636 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:02:27,755 basehttp 6828 25636 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19703
INFO 2025-06-14 12:02:28,071 basehttp 6828 25636 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:06:15,275 basehttp 6828 24948 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:06:16,175 basehttp 6828 24948 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19777
INFO 2025-06-14 12:06:16,645 basehttp 6828 24948 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:00,040 basehttp 6828 24948 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:07:00,739 basehttp 6828 24948 "GET /admin/catalogue/category/ HTTP/1.1" 200 20614
INFO 2025-06-14 12:07:01,070 basehttp 6828 24948 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:05,910 basehttp 6828 24948 "GET /admin/catalogue/category/7/change/ HTTP/1.1" 200 20054
INFO 2025-06-14 12:07:06,222 basehttp 6828 24948 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:06,547 basehttp 6828 34616 "GET /static/admin/img/tooltag-arrowright.svg HTTP/1.1" 200 280
INFO 2025-06-14 12:07:14,034 basehttp 6828 24948 "POST /admin/catalogue/category/7/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:07:14,608 basehttp 6828 24948 "GET /admin/catalogue/category/ HTTP/1.1" 200 20648
INFO 2025-06-14 12:07:14,910 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:17,791 basehttp 6828 24948 "GET /admin/catalogue/category/7/change/ HTTP/1.1" 200 20054
INFO 2025-06-14 12:07:18,116 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:22,750 basehttp 6828 24948 "GET /admin/catalogue/category/ HTTP/1.1" 200 20412
INFO 2025-06-14 12:07:23,084 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:25,964 basehttp 6828 24948 "GET /admin/catalogue/category/6/change/ HTTP/1.1" 200 19995
INFO 2025-06-14 12:07:26,389 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:07:35,097 basehttp 6828 24948 "POST /admin/catalogue/category/6/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:07:36,107 basehttp 6828 24948 "GET /admin/catalogue/category/ HTTP/1.1" 200 20641
INFO 2025-06-14 12:07:36,457 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:08:19,693 basehttp 6828 24948 "GET /admin/catalogue/category/6/change/ HTTP/1.1" 200 19987
INFO 2025-06-14 12:08:20,035 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:08:39,339 basehttp 6828 24948 "POST /admin/catalogue/category/6/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:08:39,910 basehttp 6828 24948 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19844
INFO 2025-06-14 12:08:40,223 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:09:04,387 basehttp 6828 24948 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:09:04,982 basehttp 6828 24948 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19921
INFO 2025-06-14 12:09:05,311 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:09:15,887 basehttp 6828 24948 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:09:16,587 basehttp 6828 24948 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 20001
INFO 2025-06-14 12:09:17,042 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:09:49,124 basehttp 6828 24948 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:09:49,609 basehttp 6828 24948 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 20093
INFO 2025-06-14 12:09:49,927 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:10:02,474 basehttp 6828 24948 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:10:03,478 basehttp 6828 24948 "GET /admin/catalogue/category/ HTTP/1.1" 200 22847
INFO 2025-06-14 12:10:03,891 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:10:07,858 basehttp 6828 24948 "GET /admin/catalogue/category/7/change/ HTTP/1.1" 200 20212
INFO 2025-06-14 12:10:08,262 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:10:15,214 basehttp 6828 24948 "GET /admin/catalogue/category/ HTTP/1.1" 200 22603
INFO 2025-06-14 12:10:15,614 basehttp 6828 34616 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:14:38,006 basehttp 6828 7028 "GET /admin/catalogue/category/10/change/ HTTP/1.1" 200 20334
INFO 2025-06-14 12:14:39,218 basehttp 6828 7028 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:14:54,200 basehttp 6828 37356 "OPTIONS /api/cart/ HTTP/1.1" 200 0
INFO 2025-06-14 12:14:54,203 basehttp 6828 37356 "OPTIONS /api/catalogue/categories/?level=0 HTTP/1.1" 200 0
INFO 2025-06-14 12:14:54,206 basehttp 6828 37356 "OPTIONS /api/catalogue/categories/?level=0 HTTP/1.1" 200 0
INFO 2025-06-14 12:14:54,207 basehttp 6828 17736 "OPTIONS /api/cart/summary/ HTTP/1.1" 200 0
INFO 2025-06-14 12:14:54,254 basehttp 6828 29092 "OPTIONS /api/cart/ HTTP/1.1" 200 0
INFO 2025-06-14 12:14:54,258 basehttp 6828 30716 "OPTIONS /api/cart/summary/ HTTP/1.1" 200 0
INFO 2025-06-14 12:14:54,641 basehttp 6828 37356 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:14:54,921 basehttp 6828 17736 "GET /api/cart/ HTTP/1.1" 200 383
INFO 2025-06-14 12:14:54,978 basehttp 6828 31780 "GET /api/cart/summary/ HTTP/1.1" 200 246
INFO 2025-06-14 12:14:55,026 basehttp 6828 37356 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:14:55,651 basehttp 6828 17736 "GET /api/cart/ HTTP/1.1" 200 383
INFO 2025-06-14 12:14:55,760 basehttp 6828 31780 "GET /api/cart/summary/ HTTP/1.1" 200 246
INFO 2025-06-14 12:15:07,084 basehttp 6828 7028 "POST /admin/catalogue/category/10/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:15:07,875 basehttp 6828 7028 "GET /admin/catalogue/category/ HTTP/1.1" 200 22841
INFO 2025-06-14 12:15:08,180 basehttp 6828 7028 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:15:11,964 basehttp 6828 7028 "GET /admin/catalogue/category/9/change/ HTTP/1.1" 200 20363
INFO 2025-06-14 12:15:12,437 basehttp 6828 27868 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:15:22,614 basehttp 6828 30216 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:15:22,897 basehttp 6828 30216 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:17:00,762 basehttp 6828 7028 "POST /admin/catalogue/category/9/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:17:01,420 basehttp 6828 36332 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:17:01,434 basehttp 6828 7028 "GET /admin/catalogue/category/ HTTP/1.1" 200 22839
INFO 2025-06-14 12:17:01,671 basehttp 6828 30380 "GET /api/cart/summary/ HTTP/1.1" 200 246
INFO 2025-06-14 12:17:01,685 basehttp 6828 36332 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:17:01,696 basehttp 6828 32032 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:17:01,803 basehttp 6828 27868 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:17:02,418 basehttp 6828 30380 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:17:02,472 basehttp 6828 32032 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:17:17,672 basehttp 6828 7028 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 19900
INFO 2025-06-14 12:17:18,144 basehttp 6828 27868 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:17:41,135 basehttp 6828 7028 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:17:41,598 basehttp 6828 7028 "GET /admin/catalogue/category/add/ HTTP/1.1" 200 20256
INFO 2025-06-14 12:17:41,885 basehttp 6828 27868 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:21:51,350 basehttp 6828 23164 "POST /admin/catalogue/category/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:21:52,200 basehttp 6828 23164 "GET /admin/catalogue/category/ HTTP/1.1" 200 23866
INFO 2025-06-14 12:21:52,545 basehttp 6828 23164 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:23:36,078 basehttp 6828 23164 "GET /admin/catalogue/service/ HTTP/1.1" 200 15888
INFO 2025-06-14 12:23:36,404 basehttp 6828 23164 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:23:40,259 basehttp 6828 23164 "GET /admin/catalogue/service/add/ HTTP/1.1" 200 23595
INFO 2025-06-14 12:23:40,555 basehttp 6828 1652 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:29:47,101 basehttp 6828 36372 "POST /admin/catalogue/service/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:29:47,631 basehttp 6828 36372 "GET /admin/catalogue/service/ HTTP/1.1" 200 18816
INFO 2025-06-14 12:29:47,778 basehttp 6828 33100 - Broken pipe from ('127.0.0.1', 65435)
INFO 2025-06-14 12:29:47,918 basehttp 6828 36372 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:29:47,983 basehttp 6828 36372 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-06-14 12:29:52,424 basehttp 6828 36372 "GET /admin/catalogue/service/add/ HTTP/1.1" 200 23595
INFO 2025-06-14 12:29:52,686 basehttp 6828 36372 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:31:34,216 basehttp 6828 36372 "POST /admin/catalogue/service/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:31:34,701 basehttp 6828 36372 "GET /admin/catalogue/service/add/ HTTP/1.1" 200 23869
INFO 2025-06-14 12:31:34,964 basehttp 6828 36372 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:36:51,083 basehttp 6828 39232 "POST /admin/catalogue/service/add/ HTTP/1.1" 302 0
INFO 2025-06-14 12:36:51,581 basehttp 6828 39232 "GET /admin/catalogue/service/ HTTP/1.1" 200 20041
INFO 2025-06-14 12:36:51,929 basehttp 6828 39232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:37:01,362 basehttp 6828 39232 "GET /admin/catalogue/service/2/change/ HTTP/1.1" 200 24224
INFO 2025-06-14 12:37:01,690 basehttp 6828 39232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:37:14,415 basehttp 6828 39232 "POST /admin/catalogue/service/2/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:37:14,894 basehttp 6828 39232 "GET /admin/catalogue/service/ HTTP/1.1" 200 20045
INFO 2025-06-14 12:37:15,195 basehttp 6828 39232 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:37:19,237 basehttp 6828 39232 "GET /admin/catalogue/service/3/change/ HTTP/1.1" 200 24224
INFO 2025-06-14 12:37:19,534 basehttp 6828 34152 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:37:29,561 basehttp 6828 39232 "POST /admin/catalogue/service/3/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:37:30,063 basehttp 6828 39232 "GET /admin/catalogue/service/ HTTP/1.1" 200 20046
INFO 2025-06-14 12:37:30,405 basehttp 6828 34152 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:39:36,819 basehttp 6828 14912 "OPTIONS /api/catalogue/services/?ordering=title HTTP/1.1" 200 0
INFO 2025-06-14 12:39:36,830 basehttp 6828 9684 "OPTIONS /api/catalogue/services/?ordering=title HTTP/1.1" 200 0
INFO 2025-06-14 12:39:37,145 basehttp 6828 9684 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:39:37,227 basehttp 6828 348 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:39:37,376 basehttp 6828 9684 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:39:37,526 basehttp 6828 348 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:41:26,671 basehttp 6828 39648 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:41:26,936 basehttp 6828 14956 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:41:26,970 basehttp 6828 39648 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:41:27,109 basehttp 6828 15668 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:41:27,129 basehttp 6828 34848 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:41:27,565 basehttp 6828 39648 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:41:27,777 basehttp 6828 15668 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:41:27,825 basehttp 6828 34848 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:41:57,444 basehttp 6828 34828 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:41:57,746 basehttp 6828 34828 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:27,049 basehttp 6828 5512 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:27,367 basehttp 6828 1928 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:44:27,407 basehttp 6828 27684 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:44:27,458 basehttp 6828 5512 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:28,134 basehttp 6828 1928 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:44:28,273 basehttp 6828 27684 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:44:34,035 basehttp 6828 17048 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:44:34,166 basehttp 6828 22064 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:34,474 basehttp 6828 17048 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:44:34,572 basehttp 6828 22064 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:55,313 basehttp 6828 35216 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:55,470 basehttp 6828 22224 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:44:55,489 basehttp 6828 32204 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:44:55,617 basehttp 6828 35216 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:44:56,065 basehttp 6828 22224 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:44:56,103 basehttp 6828 32204 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:46:46,514 basehttp 6828 26188 "GET /api/catalogue/categories/ HTTP/1.1" 200 3250
INFO 2025-06-14 12:51:17,588 basehttp 6828 12576 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:51:17,898 basehttp 6828 40956 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:51:17,906 basehttp 6828 36232 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:51:17,913 basehttp 6828 12576 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:51:18,495 basehttp 6828 40956 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:51:18,528 basehttp 6828 36232 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:52:19,323 basehttp 6828 37676 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:52:19,580 basehttp 6828 32536 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:52:19,608 basehttp 6828 39804 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:52:19,696 basehttp 6828 37676 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:52:20,331 basehttp 6828 39804 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:52:20,346 basehttp 6828 32536 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:52:27,305 basehttp 6828 3100 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:52:27,565 basehttp 6828 24788 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:52:27,725 basehttp 6828 3100 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:52:28,017 basehttp 6828 12584 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:52:28,199 basehttp 6828 24788 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:52:28,227 basehttp 6828 2052 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:52:29,090 basehttp 6828 12584 "GET /api/cart/summary/ HTTP/1.1" 200 247
INFO 2025-06-14 12:52:29,297 basehttp 6828 2052 "GET /api/cart/ HTTP/1.1" 200 384
INFO 2025-06-14 12:52:44,010 basehttp 6828 6672 "OPTIONS /api/catalogue/services/?ordering=title&category=4 HTTP/1.1" 200 0
INFO 2025-06-14 12:52:44,275 basehttp 6828 6672 "GET /api/catalogue/services/?ordering=title&category=4 HTTP/1.1" 200 52
INFO 2025-06-14 12:52:44,396 basehttp 6828 31328 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 898
INFO 2025-06-14 12:52:47,565 basehttp 6828 6672 "OPTIONS /api/catalogue/services/?ordering=title&category=14 HTTP/1.1" 200 0
INFO 2025-06-14 12:52:47,804 basehttp 6828 6672 "GET /api/catalogue/services/?ordering=title&category=14 HTTP/1.1" 200 1001
INFO 2025-06-14 12:52:57,159 basehttp 6828 25532 "OPTIONS /api/catalogue/services/?ordering=title&category=1 HTTP/1.1" 200 0
INFO 2025-06-14 12:52:57,386 basehttp 6828 25532 "GET /api/catalogue/services/?ordering=title&category=1 HTTP/1.1" 200 52
INFO 2025-06-14 12:53:11,375 basehttp 6828 31328 - Broken pipe from ('127.0.0.1', 50409)
INFO 2025-06-14 12:53:11,842 basehttp 6828 35192 "GET /admin/catalogue/category/ HTTP/1.1" 200 23626
INFO 2025-06-14 12:53:12,046 basehttp 6828 22488 - Broken pipe from ('127.0.0.1', 50460)
INFO 2025-06-14 12:53:12,227 basehttp 6828 35192 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
WARNING 2025-06-14 12:53:12,409 log 6828 35192 Not Found: /favicon.ico
INFO 2025-06-14 12:53:12,410 basehttp 6828 35192 - Broken pipe from ('127.0.0.1', 50447)
INFO 2025-06-14 12:53:16,162 basehttp 6828 30044 "GET /api/catalogue/categories/ HTTP/1.1" 200 3250
INFO 2025-06-14 12:53:16,877 basehttp 6828 37300 "GET /admin/catalogue/category/14/change/ HTTP/1.1" 200 20553
INFO 2025-06-14 12:53:17,260 basehttp 6828 37300 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:53:30,141 basehttp 6828 37300 "POST /admin/catalogue/category/14/change/ HTTP/1.1" 302 0
INFO 2025-06-14 12:53:30,955 basehttp 6828 37300 "GET /admin/catalogue/category/ HTTP/1.1" 200 23888
INFO 2025-06-14 12:53:31,263 basehttp 6828 37300 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-14 12:53:37,207 basehttp 6828 30960 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:53:37,288 basehttp 6828 28764 "GET /api/catalogue/categories/?level=0 HTTP/1.1" 200 658
INFO 2025-06-14 12:53:42,363 basehttp 6828 28764 "GET /api/catalogue/services/?ordering=title&category=1 HTTP/1.1" 200 52
INFO 2025-06-14 12:53:47,896 basehttp 6828 32764 "OPTIONS /api/catalogue/services/?ordering=title&category=2 HTTP/1.1" 200 0
INFO 2025-06-14 12:53:48,121 basehttp 6828 32764 "GET /api/catalogue/services/?ordering=title&category=2 HTTP/1.1" 200 52
INFO 2025-06-14 12:53:50,867 basehttp 6828 32764 "OPTIONS /api/catalogue/services/?ordering=title&category=3 HTTP/1.1" 200 0
INFO 2025-06-14 12:53:51,121 basehttp 6828 32764 "GET /api/catalogue/services/?ordering=title&category=3 HTTP/1.1" 200 52
INFO 2025-06-14 12:54:24,027 basehttp 6828 7984 "GET /api/catalogue/services/?ordering=title&category=1 HTTP/1.1" 200 52
INFO 2025-06-14 12:54:26,665 basehttp 6828 7984 "GET /api/catalogue/services/?ordering=title HTTP/1.1" 200 1001
INFO 2025-06-14 12:54:29,470 basehttp 6828 7984 "GET /api/catalogue/services/?ordering=title&category=2 HTTP/1.1" 200 52
INFO 2025-06-14 13:01:05,133 autoreload 6828 27236 C:\Users\<USER>\OneDrive\Documents\vinay\Projects\Home_services\django_backend\home_services\settings.py changed, reloading.
INFO 2025-06-14 13:01:09,188 autoreload 31356 32424 Watching for file changes with StatReloader
INFO 2025-06-14 13:06:58,340 autoreload 1716 29528 Watching for file changes with StatReloader
